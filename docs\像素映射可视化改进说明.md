# 像素映射可视化改进说明

## 🎯 问题背景

用户反馈原有的像素映射散点图难以理解：
- 散点分布密集，看不清映射关系
- 无法直观判断增强效果
- 三张图看起来几乎一样，无法区分差异

## 🎨 改进方案

### 📊 **新的可视化方式**

#### **1. 映射曲线图（主要显示）**
- **类型**: 从散点图改为平滑曲线
- **优势**: 
  - 清晰显示映射趋势
  - 容易识别非线性变换
  - 直观比较不同增强方法

#### **2. 理想对角线参考**
- **作用**: 显示y=x的理想映射线
- **用途**: 作为对比基准，偏离程度表示增强强度

#### **3. 显著变化区域高亮**
- **标记**: 红色圆点标记变化显著的区域
- **阈值**: 16位图像差值>1000，8位图像差值>10
- **意义**: 快速识别主要增强区域

### 🔧 **技术改进**

#### **智能数据分组**
```csharp
// 16位图像分256组，减少噪声
int groupSize = is16Bit ? 256 : 1;
int groupKey = (sourceVal / groupSize) * groupSize;
```

#### **增加采样密度**
```csharp
// 从5000个采样点增加到10000个
int targetSamples = 10000;
```

#### **平滑曲线生成**
```csharp
// 每组计算平均值，生成平滑曲线
result[group.Key] = (int)group.Value.Average();
```

## 📈 **如何读懂新的像素映射图**

### **曲线形状含义**

#### **1. 接近对角线**
```
特征: 蓝色曲线贴近灰色虚线
含义: 增强效果微弱，基本保持原值
适用: 保守增强或原图显示
```

#### **2. 上凸曲线**
```
特征: 蓝色曲线在对角线上方
含义: 提亮增强，暗部像素值增加
适用: 暗部细节增强
```

#### **3. 下凹曲线**
```
特征: 蓝色曲线在对角线下方
含义: 压暗增强，亮部像素值减少
适用: 过曝抑制
```

#### **4. S型曲线**
```
特征: 先上凸后下凹的S形
含义: 对比度增强，拉开灰度差异
适用: 整体对比度提升
```

### **红色高亮点含义**

- **密集分布**: 该灰度范围变化剧烈
- **稀疏分布**: 该灰度范围变化轻微
- **无红点**: 该区域基本无变化

### **实际应用示例**

#### **工业X射线图像**
- **低灰度区域（0-20000）**: 焊缝区域，期望上凸增强
- **中灰度区域（20000-40000）**: 工件区域，期望适度增强
- **高灰度区域（40000+）**: 背景区域，期望压缩或保持

## 🎛️ **可选显示模式**

### **差值显示模式**
如果需要更直观地看到变化量，可以使用差值显示：

```csharp
// 调用差值显示方法
DisplayPixelMappingAsDifference(sourceImage, targetImage, chart, title);
```

**差值图特点**:
- **Y轴**: 显示像素值变化量（增强值 - 原值）
- **零线**: 灰色虚线表示无变化
- **正值**: 像素值增加（提亮）
- **负值**: 像素值减少（压暗）

## 🔍 **故障排除**

### **如果曲线仍然接近对角线**

#### **可能原因**:
1. **增强参数过于保守**
   - 检查RetinexStrength、ContrastStrength等参数
   - 尝试使用Standard或Strong模式

2. **ROI区域过小**
   - 检查ROI掩码覆盖范围
   - 大部分像素可能在背景区域未被增强

3. **算法未生效**
   - 检查增强算法是否正确执行
   - 查看日志输出确认处理流程

#### **验证方法**:
```csharp
// 检查图像统计差异
Scalar originalMean = Cv2.Mean(originalImage);
Scalar enhancedMean = Cv2.Mean(enhancedImage);
Console.WriteLine($"原图平均值: {originalMean.Val0:F2}");
Console.WriteLine($"增强图平均值: {enhancedMean.Val0:F2}");
```

## 🎯 **预期效果**

使用新的可视化方式后，您应该能够：

1. **清晰看到映射趋势**: 曲线形状直观反映增强策略
2. **快速识别差异**: 不同增强方法的曲线形状明显不同
3. **定位关键区域**: 红色高亮点标示主要变化区域
4. **评估增强效果**: 通过曲线偏离对角线的程度判断增强强度

现在测试新界面，像素映射图应该更加直观易懂！

---

**更新时间**: 2025-01-30  
**编译状态**: ✅ 成功  
**新功能**: 映射曲线图 + 变化区域高亮
